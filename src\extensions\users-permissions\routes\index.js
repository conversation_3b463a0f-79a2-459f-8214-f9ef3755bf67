module.exports = {
  routes: [
    {
      method: 'PUT',
      path: '/users/me',
      handler: 'user.updateMe',
      config: {
        prefix: '',
        policies: [],
        middlewares: [],
      },
      info: {
        type: 'content-api',
      },
    },
    {
      method: 'PUT',
      path: '/users/register-collaborator',
      handler: 'user.registerCollabration',
      config: {
        prefix: '',
        policies: [],
        middlewares: [],
      },
      info: {
        type: 'content-api',
      },
    },
  ],
};
