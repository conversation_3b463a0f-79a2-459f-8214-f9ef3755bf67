{"kind": "collectionType", "collectionName": "up_users", "info": {"name": "user", "description": "", "singularName": "user", "pluralName": "users", "displayName": "User"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"username": {"type": "string", "minLength": 3, "unique": true, "configurable": false, "required": true}, "email": {"type": "email", "minLength": 6, "configurable": false, "required": true}, "provider": {"type": "string", "configurable": false}, "password": {"type": "password", "minLength": 6, "configurable": false, "private": true, "searchable": false}, "resetPasswordToken": {"type": "string", "configurable": false, "private": true, "searchable": false}, "confirmationToken": {"type": "string", "configurable": false, "private": true, "searchable": false}, "confirmed": {"type": "boolean", "default": false, "configurable": false}, "blocked": {"type": "boolean", "default": false, "configurable": false}, "role": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.role", "inversedBy": "users", "configurable": false}, "name": {"type": "string", "required": true}, "phone": {"type": "string", "required": true, "unique": true}, "balance": {"type": "decimal", "default": 0}, "referUser": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "rank": {"type": "relation", "relation": "oneToOne", "target": "api::cai-dat-cap-bac.cai-dat-cap-bac"}, "taxCode": {"type": "string", "required": false}, "avatarUrl": {"type": "string", "required": false}, "isZaloOA": {"type": "boolean", "required": false}, "bankName": {"type": "string", "required": false}, "bankAccount": {"type": "string", "required": false}, "bankOwner": {"type": "string", "required": false}, "cccd": {"type": "string", "required": false}, "zaloId": {"type": "string", "required": false, "unique": true}, "zaloName": {"type": "string", "required": false}, "zaloAvatar": {"type": "string", "required": false}, "zaloAccessToken": {"type": "string", "required": false, "private": true}, "lastZaloLogin": {"type": "datetime", "required": false}}}